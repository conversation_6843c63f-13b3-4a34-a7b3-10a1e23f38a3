BUSINESS_DB_HOST=rr-bp19zkvz53m17h8qsmo.mysql.rds.aliyuncs.com
BUSINESS_DB_PORT=3306
BUSINESS_DB_USER=xianmu_ai
BUSINESS_DB_PASSWORD=Xianmu_ai
BUSINESS_DB_NAME=xianmudb

CHATBI_DB_HOST=mysql-xm.summerfarm.net
CHATBI_DB_PORT=3308
CHATBI_DB_USER=test
CHATBI_DB_PASSWORD=xianmu619
CHATBI_DB_NAME=chatbi_test


# --- Model Provider Configuration ---
# 鲜沐内部API配置
PROVIDER_XM_API_KEY=sk-Is03LB8g58Qz41bdQKip5g
PROVIDER_XM_API_BASE=https://litellm-test.summerfarm.net/v1
PROVIDER_XM_DEFAULT_MODEL=kimi-k2
PROVIDER_XM_FAST_MODEL=kimi-k2

# OpenRouter API配置
PROVIDER_OPENROUTER_API_KEY=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
PROVIDER_OPENROUTER_API_BASE=https://openrouter.ai/api/v1
PROVIDER_OPENROUTER_DEFAULT_MODEL=google/gemini-2.5-pro
PROVIDER_OPENROUTER_FAST_MODEL=openai/gpt-4.1-mini
PROVIDER_OPENROUTER_CLAUDE_MODEL=anthropic/claude-sonnet-4

# 默认提供者配置
DEFAULT_MODEL_PROVIDER=openrouter

TOP_TABLE_CNT=20
CHAT_BI_HOST_NAME=https://testchat-bi.summerfarm.net
APPLICATION_ROOT=
MIN_ROWS_TO_IMPORT=20

# 如果超过400行，则对喂给AI的数据进行截断
MIN_ROWS_TO_CUTOFF=400
MINUTES_TO_FORCE_REFRESH_TOKEN=10
FEISHU_APP_ID=cli_a450bff26fbbd00d
FEISHU_APP_SECRET=uQolgem8B8fwuTlsER0bZdUe7xZjueHU
ENABLE_BOT_MESSAGE_PROCESSING=false
CHATBI_MYSQL_DATABASE=chatbi_test
STREAM_TIMEOUT_MINUTES=10

# Bad Case通知群聊ID（可选）
# 当用户标记对话为Bad Case时，会发送通知到此群聊
# 如果不配置此项，则不会发送通知
BAD_CASE_NOTIFICATION_CHAT_ID=oc_a50d15dbcfa9b3a7667d016cd820b1f0

# 群聊时，@机器人的名称
FEISHU_BOT_NAME=ChatBI测试
LOG_SERVER_NAME=true

# 欢迎消息的时间间隔（小时）
USER_ENTERED_THRESHOLD=24

ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5tQzmpz2nQEWdiqvQGsc
ALIBABA_CLOUD_ACCESS_KEY_SECRET=******************************


SUMMERFARM_APP_API_PREFIX=https://admin.summerfarm.net